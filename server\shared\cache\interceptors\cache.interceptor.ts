/**
 * 缓存拦截器
 * 用于缓存API响应
 */
import { Injectable, NestInterceptor, ExecutionContext, CallHandler, Inject } from '@nestjs/common';
import { Observable, of } from 'rxjs';
import { tap, switchMap } from 'rxjs/operators';
import { CacheService } from '../cache.service';
import { Reflector } from '@nestjs/core';
import { CACHE_KEY_METADATA, CACHE_TTL_METADATA } from '../decorators/cache.constants';
import { Request } from 'express';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  /**
   * 拦截请求
   * @param context 执行上下文
   * @param next 下一个处理器
   */
  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    // 获取请求对象
    const request = context.switchToHttp().getRequest<Request>();

    // 如果是非GET请求，不进行缓存
    if (request.method !== 'GET') {
      return next.handle();
    }

    // 获取缓存键
    const cacheKey = this.getCacheKey(context);

    // 获取缓存TTL
    const ttl = this.reflector.get(CACHE_TTL_METADATA, context.getHandler());

    // 尝试从缓存获取数据
    try {
      const cachedData = await this.cacheService.get(cacheKey, async () => {
        return await next.handle().pipe(
          switchMap(data => of(data))
        ).toPromise();
      }, ttl);
      return of(cachedData);
    } catch (error) {
      // 如果获取缓存失败，直接执行处理器
      return next.handle();
    }
  }

  /**
   * 获取缓存键
   * @param context 执行上下文
   * @returns 缓存键
   */
  private getCacheKey(context: ExecutionContext): string {
    // 获取请求对象
    const request = context.switchToHttp().getRequest<Request>();

    // 获取自定义缓存键
    const customCacheKey = this.reflector.get(CACHE_KEY_METADATA, context.getHandler());

    if (customCacheKey) {
      return customCacheKey;
    }

    // 生成缓存键
    const url = request.originalUrl || request.url;
    return `http-cache:${url}`;
  }
}
